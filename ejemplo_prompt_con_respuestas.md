# Ejemplo del Prompt Mejorado con Respuestas de Evaluación

## Contexto Completo que Ahora Recibe la IA

Cuando un usuario hace clic en "Mejorar con IA", el sistema ahora envía un prompt que incluye:

### 1. **Información Básica**
```
CONTEXTO:
- Factor de competencia: Bloque 0 - Fundamentos
- Evaluado: <PERSON>
- Proyecto: AI Strategy
```

### 2. **Competencias del Factor**
```
COMPETENCIAS ESPECÍFICAS EN ESTE FACTOR:
  • Fundamentos de Programación: Conocimiento básico de conceptos de programación, estructuras de datos y algoritmos
  • Metodologías de Desarrollo: Comprensión de metodologías ágiles y tradicionales de desarrollo de software
  • Control de Versiones: Manejo de sistemas de control de versiones como Git
```

### 3. **Respuestas de la Evaluación** (¡NUEVO!)
```
RESPUESTAS DE LA EVALUACIÓN ACTUAL:
  • Fundamentos de Programación:
    - ¿Comprende los conceptos básicos de programación orientada a objetos?: No
    - ¿Puede implementar estructuras de datos básicas como listas y diccionarios?: A veces
    - ¿Entiende la complejidad algorítmica y optimización de código?: No
  • Metodologías de Desarrollo:
    - ¿Conoce los principios de metodologías ágiles como Scrum?: Sí
    - ¿Puede participar efectivamente en ceremonias ágiles?: Sí
    - ¿Comprende el ciclo de vida del desarrollo de software?: A veces
  • Control de Versiones:
    - ¿Maneja comandos básicos de Git (commit, push, pull)?: A veces
    - ¿Puede resolver conflictos de merge?: No
    - ¿Entiende el flujo de trabajo con ramas (branching)?: No
```

### 4. **Plan de Acción Original**
```
Ana necesita fortalecer sus habilidades técnicas fundamentales.
```

### 5. **Instrucciones Mejoradas**
```
INSTRUCCIONES:
1. Mejora el plan de acción original haciéndolo más específico, medible y accionable
2. Utiliza las competencias específicas listadas arriba para crear actividades de desarrollo dirigidas
3. ANALIZA las respuestas de evaluación para identificar fortalezas y áreas de mejora:
   - Respuestas "Sí": Fortalezas que se pueden aprovechar y mantener
   - Respuestas "No": Áreas críticas que requieren desarrollo prioritario
   - Respuestas "A veces": Competencias parciales que necesitan refuerzo y consistencia
4. Incluye objetivos SMART (Específicos, Medibles, Alcanzables, Relevantes, Temporales)
5. Sugiere actividades concretas y recursos específicos que desarrollen las competencias mencionadas
6. PRIORIZA las actividades basándote en las respuestas "No" y "A veces" identificadas
7. Incluye estrategias para aprovechar las fortalezas existentes (respuestas "Sí")
8. Mantén un tono profesional pero cercano
9. Estructura el contenido de manera clara y organizada y sintetica
10. Incluye indicadores de progreso o métricas cuando sea apropiado
11. Conecta directamente las actividades propuestas con las competencias del factor
12. Mantén la esencia y el contexto del plan original
13. Escribe en español con un estilo profesional
```

## Resultado Esperado Mejorado

Con esta información detallada, la IA ahora puede generar planes de acción altamente personalizados como:

```
PLAN DE DESARROLLO TÉCNICO PARA ANA GARCÍA LÓPEZ - PROYECTO AI STRATEGY

ANÁLISIS DE FORTALEZAS Y ÁREAS DE MEJORA:

FORTALEZAS IDENTIFICADAS (aprovechar):
- Conocimiento sólido de metodologías ágiles y Scrum
- Participación efectiva en ceremonias ágiles

ÁREAS CRÍTICAS (prioridad alta):
- Programación orientada a objetos
- Complejidad algorítmica y optimización
- Resolución de conflictos en Git
- Flujo de trabajo con ramas

ÁREAS PARCIALES (refuerzo necesario):
- Implementación de estructuras de datos
- Ciclo de vida del desarrollo
- Comandos básicos de Git

PLAN DE ACCIÓN PRIORITIZADO (8 semanas):

FASE 1: FUNDAMENTOS CRÍTICOS (Semanas 1-4)
Objetivo: Desarrollar base sólida en programación orientada a objetos
- Curso intensivo de POO en Python/Java (20 horas)
- Implementar 3 proyectos pequeños aplicando POO
- Métrica: Completar evaluación técnica con 80% de acierto

FASE 2: ESTRUCTURAS Y ALGORITMOS (Semanas 3-6)
Objetivo: Dominar estructuras de datos y análisis de complejidad
- Práctica diaria en plataforma como LeetCode (1 hora/día)
- Implementar 5 estructuras de datos desde cero
- Métrica: Resolver 20 problemas de complejidad media

FASE 3: CONTROL DE VERSIONES AVANZADO (Semanas 5-7)
Objetivo: Dominar Git para trabajo colaborativo
- Tutorial avanzado de Git y resolución de conflictos
- Práctica con repositorio del proyecto AI Strategy
- Crear y gestionar 3 ramas con merge exitoso
- Métrica: Resolver 5 conflictos de merge sin asistencia

APROVECHAMIENTO DE FORTALEZAS:
- Liderar retrospectivas técnicas del equipo
- Mentorizar a otros en metodologías ágiles
- Documentar mejores prácticas de Scrum para el proyecto

SEGUIMIENTO SEMANAL:
Revisiones con líder técnico enfocadas en áreas críticas identificadas
```

## Beneficios de la Nueva Mejora

1. **Análisis Específico**: La IA identifica exactamente qué competencias están fuertes vs. débiles
2. **Priorización Inteligente**: Se enfocan recursos en las áreas más críticas (respuestas "No")
3. **Aprovechamiento de Fortalezas**: Incluye estrategias para usar las competencias fuertes
4. **Desarrollo Gradual**: Aborda las competencias parciales ("A veces") de manera progresiva
5. **Personalización Total**: Cada plan es único basado en las respuestas específicas del evaluado

Esta mejora transforma el botón "Mejorar con IA" en una herramienta de coaching personalizado que genera planes de desarrollo verdaderamente específicos y accionables.
